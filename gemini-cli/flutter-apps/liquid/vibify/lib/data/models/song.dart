import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'song.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class Song extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String artist;

  @HiveField(3)
  final String? album;

  @HiveField(4)
  final String? albumArt;

  @HiveField(5)
  final int durationMs;

  @HiveField(6)
  final String filePath;

  @HiveField(7)
  final String? genre;

  @HiveField(8)
  final int? year;

  @HiveField(9)
  final int? trackNumber;

  @HiveField(10)
  final bool isFavorite;

  @HiveField(11)
  final int playCount;

  @HiveField(12)
  final DateTime? lastPlayed;

  @HiveField(13)
  final DateTime dateAdded;

  @HiveField(14)
  final int? bitrate;

  @HiveField(15)
  final String? format;

  const Song({
    required this.id,
    required this.title,
    required this.artist,
    this.album,
    this.albumArt,
    required this.durationMs,
    required this.filePath,
    this.genre,
    this.year,
    this.trackNumber,
    this.isFavorite = false,
    this.playCount = 0,
    this.lastPlayed,
    required this.dateAdded,
    this.bitrate,
    this.format,
  });

  factory Song.fromJson(Map<String, dynamic> json) => _$SongFromJson(json);
  Map<String, dynamic> toJson() => _$SongToJson(this);

  Song copyWith({
    String? id,
    String? title,
    String? artist,
    String? album,
    String? albumArt,
    int? durationMs,
    String? filePath,
    String? genre,
    int? year,
    int? trackNumber,
    bool? isFavorite,
    int? playCount,
    DateTime? lastPlayed,
    DateTime? dateAdded,
    int? bitrate,
    String? format,
  }) {
    return Song(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      albumArt: albumArt ?? this.albumArt,
      durationMs: durationMs ?? this.durationMs,
      filePath: filePath ?? this.filePath,
      genre: genre ?? this.genre,
      year: year ?? this.year,
      trackNumber: trackNumber ?? this.trackNumber,
      isFavorite: isFavorite ?? this.isFavorite,
      playCount: playCount ?? this.playCount,
      lastPlayed: lastPlayed ?? this.lastPlayed,
      dateAdded: dateAdded ?? this.dateAdded,
      bitrate: bitrate ?? this.bitrate,
      format: format ?? this.format,
    );
  }

  String get displayArtist => artist.isEmpty ? 'Unknown Artist' : artist;
  String get displayAlbum =>
      album?.isEmpty == true ? 'Unknown Album' : album ?? 'Unknown Album';
  String get displayTitle => title.isEmpty ? 'Unknown Title' : title;

  Duration get duration => Duration(milliseconds: durationMs);

  String get durationString {
    final duration = Duration(milliseconds: durationMs);
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  bool get hasAlbumArt => albumArt != null && albumArt!.isNotEmpty;

  @override
  List<Object?> get props => [
    id,
    title,
    artist,
    album,
    albumArt,
    durationMs,
    filePath,
    genre,
    year,
    trackNumber,
    isFavorite,
    playCount,
    lastPlayed,
    dateAdded,
    bitrate,
    format,
  ];

  @override
  String toString() {
    return 'Song(id: $id, title: $title, artist: $artist, album: $album, duration: $durationString)';
  }
}
