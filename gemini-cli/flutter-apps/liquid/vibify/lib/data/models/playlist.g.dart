// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'playlist.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PlaylistAdapter extends TypeAdapter<Playlist> {
  @override
  final int typeId = 1;

  @override
  Playlist read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Playlist(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String?,
      songIds: (fields[3] as List).cast<String>(),
      coverArt: fields[4] as String?,
      createdAt: fields[5] as DateTime,
      updatedAt: fields[6] as DateTime,
      isSystemPlaylist: fields[7] as bool,
      type: fields[8] as PlaylistType,
      playCount: fields[9] as int,
    );
  }

  @override
  void write(BinaryWriter writer, Playlist obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.songIds)
      ..writeByte(4)
      ..write(obj.coverArt)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.updatedAt)
      ..writeByte(7)
      ..write(obj.isSystemPlaylist)
      ..writeByte(8)
      ..write(obj.type)
      ..writeByte(9)
      ..write(obj.playCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PlaylistAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PlaylistTypeAdapter extends TypeAdapter<PlaylistType> {
  @override
  final int typeId = 2;

  @override
  PlaylistType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PlaylistType.custom;
      case 1:
        return PlaylistType.favorites;
      case 2:
        return PlaylistType.recentlyPlayed;
      case 3:
        return PlaylistType.mostPlayed;
      case 4:
        return PlaylistType.recentlyAdded;
      case 5:
        return PlaylistType.queue;
      default:
        return PlaylistType.custom;
    }
  }

  @override
  void write(BinaryWriter writer, PlaylistType obj) {
    switch (obj) {
      case PlaylistType.custom:
        writer.writeByte(0);
        break;
      case PlaylistType.favorites:
        writer.writeByte(1);
        break;
      case PlaylistType.recentlyPlayed:
        writer.writeByte(2);
        break;
      case PlaylistType.mostPlayed:
        writer.writeByte(3);
        break;
      case PlaylistType.recentlyAdded:
        writer.writeByte(4);
        break;
      case PlaylistType.queue:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PlaylistTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Playlist _$PlaylistFromJson(Map<String, dynamic> json) => Playlist(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      songIds:
          (json['songIds'] as List<dynamic>).map((e) => e as String).toList(),
      coverArt: json['coverArt'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isSystemPlaylist: json['isSystemPlaylist'] as bool? ?? false,
      type: $enumDecodeNullable(_$PlaylistTypeEnumMap, json['type']) ??
          PlaylistType.custom,
      playCount: (json['playCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$PlaylistToJson(Playlist instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'songIds': instance.songIds,
      'coverArt': instance.coverArt,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isSystemPlaylist': instance.isSystemPlaylist,
      'type': _$PlaylistTypeEnumMap[instance.type]!,
      'playCount': instance.playCount,
    };

const _$PlaylistTypeEnumMap = {
  PlaylistType.custom: 'custom',
  PlaylistType.favorites: 'favorites',
  PlaylistType.recentlyPlayed: 'recentlyPlayed',
  PlaylistType.mostPlayed: 'mostPlayed',
  PlaylistType.recentlyAdded: 'recentlyAdded',
  PlaylistType.queue: 'queue',
};
