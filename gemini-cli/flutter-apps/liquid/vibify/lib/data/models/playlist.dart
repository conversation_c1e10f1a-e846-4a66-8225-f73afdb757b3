import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import 'song.dart';

part 'playlist.g.dart';

@HiveType(typeId: 1)
@JsonSerializable()
class Playlist extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String? description;

  @HiveField(3)
  final List<String> songIds;

  @HiveField(4)
  final String? coverArt;

  @HiveField(5)
  final DateTime createdAt;

  @HiveField(6)
  final DateTime updatedAt;

  @HiveField(7)
  final bool isSystemPlaylist;

  @HiveField(8)
  final PlaylistType type;

  @HiveField(9)
  final int playCount;

  const Playlist({
    required this.id,
    required this.name,
    this.description,
    required this.songIds,
    this.coverArt,
    required this.createdAt,
    required this.updatedAt,
    this.isSystemPlaylist = false,
    this.type = PlaylistType.custom,
    this.playCount = 0,
  });

  factory Playlist.fromJson(Map<String, dynamic> json) =>
      _$PlaylistFromJson(json);
  Map<String, dynamic> toJson() => _$PlaylistToJson(this);

  Playlist copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? songIds,
    String? coverArt,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSystemPlaylist,
    PlaylistType? type,
    int? playCount,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      songIds: songIds ?? this.songIds,
      coverArt: coverArt ?? this.coverArt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSystemPlaylist: isSystemPlaylist ?? this.isSystemPlaylist,
      type: type ?? this.type,
      playCount: playCount ?? this.playCount,
    );
  }

  int get songCount => songIds.length;
  bool get isEmpty => songIds.isEmpty;
  bool get isNotEmpty => songIds.isNotEmpty;

  Duration getTotalDuration(List<Song> songs) {
    int totalSeconds = 0;
    for (final songId in songIds) {
      final song = songs.firstWhere(
        (s) => s.id == songId,
        orElse: () => Song(
          id: '',
          title: '',
          artist: '',
          duration: Duration.zero,
          filePath: '',
          dateAdded: DateTime.now(),
        ),
      );
      totalSeconds += song.duration.inSeconds;
    }
    return Duration(seconds: totalSeconds);
  }

  String getTotalDurationString(List<Song> songs) {
    final duration = getTotalDuration(songs);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  Playlist addSong(String songId) {
    if (songIds.contains(songId)) return this;

    return copyWith(songIds: [...songIds, songId], updatedAt: DateTime.now());
  }

  Playlist removeSong(String songId) {
    if (!songIds.contains(songId)) return this;

    return copyWith(
      songIds: songIds.where((id) => id != songId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  Playlist reorderSongs(int oldIndex, int newIndex) {
    final newSongIds = List<String>.from(songIds);
    final item = newSongIds.removeAt(oldIndex);
    newSongIds.insert(newIndex, item);

    return copyWith(songIds: newSongIds, updatedAt: DateTime.now());
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    songIds,
    coverArt,
    createdAt,
    updatedAt,
    isSystemPlaylist,
    type,
    playCount,
  ];

  @override
  String toString() {
    return 'Playlist(id: $id, name: $name, songCount: $songCount, type: $type)';
  }
}

@HiveType(typeId: 2)
enum PlaylistType {
  @HiveField(0)
  custom,

  @HiveField(1)
  favorites,

  @HiveField(2)
  recentlyPlayed,

  @HiveField(3)
  mostPlayed,

  @HiveField(4)
  recentlyAdded,

  @HiveField(5)
  queue,
}
