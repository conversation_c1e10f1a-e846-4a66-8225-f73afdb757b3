// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'album.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AlbumAdapter extends TypeAdapter<Album> {
  @override
  final int typeId = 3;

  @override
  Album read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Album(
      id: fields[0] as String,
      name: fields[1] as String,
      artist: fields[2] as String,
      albumArt: fields[3] as String?,
      year: fields[4] as int?,
      genre: fields[5] as String?,
      songIds: (fields[6] as List).cast<String>(),
      dateAdded: fields[7] as DateTime,
      playCount: fields[8] as int,
      lastPlayed: fields[9] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, Album obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.artist)
      ..writeByte(3)
      ..write(obj.albumArt)
      ..writeByte(4)
      ..write(obj.year)
      ..writeByte(5)
      ..write(obj.genre)
      ..writeByte(6)
      ..write(obj.songIds)
      ..writeByte(7)
      ..write(obj.dateAdded)
      ..writeByte(8)
      ..write(obj.playCount)
      ..writeByte(9)
      ..write(obj.lastPlayed);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AlbumAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Album _$AlbumFromJson(Map<String, dynamic> json) => Album(
      id: json['id'] as String,
      name: json['name'] as String,
      artist: json['artist'] as String,
      albumArt: json['albumArt'] as String?,
      year: (json['year'] as num?)?.toInt(),
      genre: json['genre'] as String?,
      songIds:
          (json['songIds'] as List<dynamic>).map((e) => e as String).toList(),
      dateAdded: DateTime.parse(json['dateAdded'] as String),
      playCount: (json['playCount'] as num?)?.toInt() ?? 0,
      lastPlayed: json['lastPlayed'] == null
          ? null
          : DateTime.parse(json['lastPlayed'] as String),
    );

Map<String, dynamic> _$AlbumToJson(Album instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'artist': instance.artist,
      'albumArt': instance.albumArt,
      'year': instance.year,
      'genre': instance.genre,
      'songIds': instance.songIds,
      'dateAdded': instance.dateAdded.toIso8601String(),
      'playCount': instance.playCount,
      'lastPlayed': instance.lastPlayed?.toIso8601String(),
    };
