// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'song.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SongAdapter extends TypeAdapter<Song> {
  @override
  final int typeId = 0;

  @override
  Song read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Song(
      id: fields[0] as String,
      title: fields[1] as String,
      artist: fields[2] as String,
      album: fields[3] as String?,
      albumArt: fields[4] as String?,
      durationMs: fields[5] as int,
      filePath: fields[6] as String,
      genre: fields[7] as String?,
      year: fields[8] as int?,
      trackNumber: fields[9] as int?,
      isFavorite: fields[10] as bool,
      playCount: fields[11] as int,
      lastPlayed: fields[12] as DateTime?,
      dateAdded: fields[13] as DateTime,
      bitrate: fields[14] as int?,
      format: fields[15] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Song obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.artist)
      ..writeByte(3)
      ..write(obj.album)
      ..writeByte(4)
      ..write(obj.albumArt)
      ..writeByte(5)
      ..write(obj.durationMs)
      ..writeByte(6)
      ..write(obj.filePath)
      ..writeByte(7)
      ..write(obj.genre)
      ..writeByte(8)
      ..write(obj.year)
      ..writeByte(9)
      ..write(obj.trackNumber)
      ..writeByte(10)
      ..write(obj.isFavorite)
      ..writeByte(11)
      ..write(obj.playCount)
      ..writeByte(12)
      ..write(obj.lastPlayed)
      ..writeByte(13)
      ..write(obj.dateAdded)
      ..writeByte(14)
      ..write(obj.bitrate)
      ..writeByte(15)
      ..write(obj.format);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SongAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Song _$SongFromJson(Map<String, dynamic> json) => Song(
      id: json['id'] as String,
      title: json['title'] as String,
      artist: json['artist'] as String,
      album: json['album'] as String?,
      albumArt: json['albumArt'] as String?,
      durationMs: (json['durationMs'] as num).toInt(),
      filePath: json['filePath'] as String,
      genre: json['genre'] as String?,
      year: (json['year'] as num?)?.toInt(),
      trackNumber: (json['trackNumber'] as num?)?.toInt(),
      isFavorite: json['isFavorite'] as bool? ?? false,
      playCount: (json['playCount'] as num?)?.toInt() ?? 0,
      lastPlayed: json['lastPlayed'] == null
          ? null
          : DateTime.parse(json['lastPlayed'] as String),
      dateAdded: DateTime.parse(json['dateAdded'] as String),
      bitrate: (json['bitrate'] as num?)?.toInt(),
      format: json['format'] as String?,
    );

Map<String, dynamic> _$SongToJson(Song instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'artist': instance.artist,
      'album': instance.album,
      'albumArt': instance.albumArt,
      'durationMs': instance.durationMs,
      'filePath': instance.filePath,
      'genre': instance.genre,
      'year': instance.year,
      'trackNumber': instance.trackNumber,
      'isFavorite': instance.isFavorite,
      'playCount': instance.playCount,
      'lastPlayed': instance.lastPlayed?.toIso8601String(),
      'dateAdded': instance.dateAdded.toIso8601String(),
      'bitrate': instance.bitrate,
      'format': instance.format,
    };
