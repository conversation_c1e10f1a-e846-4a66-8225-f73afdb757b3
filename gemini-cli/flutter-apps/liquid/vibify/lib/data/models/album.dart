import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import 'song.dart';

part 'album.g.dart';

@HiveType(typeId: 3)
@JsonSerializable()
class Album extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String artist;

  @HiveField(3)
  final String? albumArt;

  @HiveField(4)
  final int? year;

  @HiveField(5)
  final String? genre;

  @HiveField(6)
  final List<String> songIds;

  @HiveField(7)
  final DateTime dateAdded;

  @HiveField(8)
  final int playCount;

  @HiveField(9)
  final DateTime? lastPlayed;

  const Album({
    required this.id,
    required this.name,
    required this.artist,
    this.albumArt,
    this.year,
    this.genre,
    required this.songIds,
    required this.dateAdded,
    this.playCount = 0,
    this.lastPlayed,
  });

  factory Album.fromJson(Map<String, dynamic> json) => _$AlbumFromJson(json);
  Map<String, dynamic> toJson() => _$AlbumToJson(this);

  Album copyWith({
    String? id,
    String? name,
    String? artist,
    String? albumArt,
    int? year,
    String? genre,
    List<String>? songIds,
    DateTime? dateAdded,
    int? playCount,
    DateTime? lastPlayed,
  }) {
    return Album(
      id: id ?? this.id,
      name: name ?? this.name,
      artist: artist ?? this.artist,
      albumArt: albumArt ?? this.albumArt,
      year: year ?? this.year,
      genre: genre ?? this.genre,
      songIds: songIds ?? this.songIds,
      dateAdded: dateAdded ?? this.dateAdded,
      playCount: playCount ?? this.playCount,
      lastPlayed: lastPlayed ?? this.lastPlayed,
    );
  }

  String get displayName => name.isEmpty ? 'Unknown Album' : name;
  String get displayArtist => artist.isEmpty ? 'Unknown Artist' : artist;
  int get trackCount => songIds.length;
  bool get hasAlbumArt => albumArt != null && albumArt!.isNotEmpty;

  Duration getTotalDuration(List<Song> songs) {
    int totalSeconds = 0;
    for (final songId in songIds) {
      final song = songs.firstWhere(
        (s) => s.id == songId,
        orElse: () => Song(
          id: '',
          title: '',
          artist: '',
          duration: Duration.zero,
          filePath: '',
          dateAdded: DateTime.now(),
        ),
      );
      totalSeconds += song.duration.inSeconds;
    }
    return Duration(seconds: totalSeconds);
  }

  String getTotalDurationString(List<Song> songs) {
    final duration = getTotalDuration(songs);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  List<Song> getSongs(List<Song> allSongs) {
    return allSongs.where((song) => songIds.contains(song.id)).toList()
      ..sort((a, b) => (a.trackNumber ?? 0).compareTo(b.trackNumber ?? 0));
  }

  String get yearString => year?.toString() ?? 'Unknown Year';

  @override
  List<Object?> get props => [
    id,
    name,
    artist,
    albumArt,
    year,
    genre,
    songIds,
    dateAdded,
    playCount,
    lastPlayed,
  ];

  @override
  String toString() {
    return 'Album(id: $id, name: $name, artist: $artist, trackCount: $trackCount, year: $year)';
  }
}
