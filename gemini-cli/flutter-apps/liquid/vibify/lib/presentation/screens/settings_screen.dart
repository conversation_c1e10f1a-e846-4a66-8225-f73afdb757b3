import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../widgets/liquid_container.dart';
import '../widgets/liquid_button.dart';
import '../widgets/liquid_progress_bar.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _listController;
  late Animation<double> _headerAnimation;
  late Animation<double> _listAnimation;

  // Equalizer settings
  List<double> _eqBands = [0.5, 0.6, 0.7, 0.5, 0.4, 0.6, 0.8, 0.5];
  final List<String> _eqLabels = [
    '60Hz',
    '170Hz',
    '310Hz',
    '600Hz',
    '1kHz',
    '3kHz',
    '6kHz',
    '12kHz',
  ];

  // Other settings
  bool _crossfadeEnabled = true;
  double _crossfadeDuration = 3.0;
  bool _sleepTimerEnabled = false;
  double _sleepTimerMinutes = 30.0;
  bool _gaplessPlayback = true;
  bool _replayGain = false;
  double _preAmp = 0.5;

  @override
  void initState() {
    super.initState();

    _headerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _listController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _headerAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _headerController, curve: Curves.easeOutCubic),
    );

    _listAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _listController, curve: Curves.easeOutCubic),
    );

    _headerController.forward();
    _listController.forward();
  }

  @override
  void dispose() {
    _headerController.dispose();
    _listController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Audio Settings',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isDark ? AppColors.darkGradient : AppColors.primaryGradient,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildHeader(),
                const SizedBox(height: 24),
                _buildEqualizer(),
                const SizedBox(height: 24),
                _buildAudioSettings(),
                const SizedBox(height: 24),
                _buildPlaybackSettings(),
                const SizedBox(height: 24),
                _buildSleepTimer(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return AnimatedBuilder(
      animation: _headerAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _headerAnimation.value)),
          child: Opacity(
            opacity: _headerAnimation.value,
            child: LiquidContainer(
              height: 120,
              gradientColors: AppColors.secondaryGradient,
              enableGlassmorphism: true,
              ultraTransparent: true,
              blurIntensity: 30.0,
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.white.withOpacity(0.2),
                      ),
                      child: const Icon(
                        Icons.equalizer,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Audio Enhancement',
                            style: AppTextStyles.headlineSmall.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Customize your listening experience',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.white.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEqualizer() {
    return AnimatedBuilder(
      animation: _listAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - _listAnimation.value)),
          child: Opacity(
            opacity: _listAnimation.value,
            child: LiquidContainer(
              gradientColors: AppColors.glassGradientLight,
              enableGlassmorphism: true,
              ultraTransparent: true,
              blurIntensity: 25.0,
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.equalizer,
                          color: Colors.white,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Equalizer',
                          style: AppTextStyles.titleLarge.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Spacer(),
                        LiquidButton(
                          height: 32,
                          gradientColors: AppColors.accentGradient,
                          onPressed: _resetEqualizer,
                          child: const Text(
                            'Reset',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: List.generate(_eqBands.length, (index) {
                        return Column(
                          children: [
                            SizedBox(
                              height: 120,
                              width: 30,
                              child: RotatedBox(
                                quarterTurns: 3,
                                child: LiquidProgressBar(
                                  value: _eqBands[index],
                                  height: 30,
                                  isInteractive: true,
                                  showBubbles: false,
                                  gradientColors: [
                                    AppColors.liquidCyan,
                                    AppColors.liquidBlue,
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      _eqBands[index] = value;
                                    });
                                  },
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _eqLabels[index],
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 10,
                              ),
                            ),
                          ],
                        );
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAudioSettings() {
    return LiquidContainer(
      gradientColors: AppColors.glassGradientLight,
      enableGlassmorphism: true,
      ultraTransparent: true,
      blurIntensity: 20.0,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.tune, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Audio Enhancement',
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSettingItem(
              'Pre-amplifier',
              'Adjust overall volume level',
              LiquidProgressBar(
                value: _preAmp,
                height: 6,
                isInteractive: true,
                showBubbles: false,
                gradientColors: [AppColors.liquidPink, AppColors.liquidPurple],
                onChanged: (value) {
                  setState(() {
                    _preAmp = value;
                  });
                },
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchItem(
              'Replay Gain',
              'Normalize volume across tracks',
              _replayGain,
              (value) {
                setState(() {
                  _replayGain = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaybackSettings() {
    return LiquidContainer(
      gradientColors: AppColors.glassGradientLight,
      enableGlassmorphism: true,
      ultraTransparent: true,
      blurIntensity: 18.0,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.play_circle_outline,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Playback',
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSwitchItem(
              'Crossfade',
              'Smooth transitions between songs',
              _crossfadeEnabled,
              (value) {
                setState(() {
                  _crossfadeEnabled = value;
                });
              },
            ),
            if (_crossfadeEnabled) ...[
              const SizedBox(height: 16),
              _buildSettingItem(
                'Crossfade Duration',
                '${_crossfadeDuration.round()} seconds',
                LiquidProgressBar(
                  value: _crossfadeDuration / 10.0,
                  height: 6,
                  isInteractive: true,
                  showBubbles: false,
                  gradientColors: AppColors.accentGradient,
                  onChanged: (value) {
                    setState(() {
                      _crossfadeDuration = value * 10.0;
                    });
                  },
                ),
              ),
            ],
            const SizedBox(height: 16),
            _buildSwitchItem(
              'Gapless Playback',
              'No silence between tracks',
              _gaplessPlayback,
              (value) {
                setState(() {
                  _gaplessPlayback = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSleepTimer() {
    return LiquidContainer(
      gradientColors: AppColors.glassGradientLight,
      enableGlassmorphism: true,
      ultraTransparent: true,
      blurIntensity: 22.0,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bedtime, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Sleep Timer',
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSwitchItem(
              'Enable Sleep Timer',
              'Automatically stop playback',
              _sleepTimerEnabled,
              (value) {
                setState(() {
                  _sleepTimerEnabled = value;
                });
              },
            ),
            if (_sleepTimerEnabled) ...[
              const SizedBox(height: 16),
              _buildSettingItem(
                'Timer Duration',
                '${_sleepTimerMinutes.round()} minutes',
                LiquidProgressBar(
                  value: _sleepTimerMinutes / 120.0,
                  height: 6,
                  isInteractive: true,
                  showBubbles: false,
                  gradientColors: [AppColors.liquidTeal, AppColors.liquidCyan],
                  onChanged: (value) {
                    setState(() {
                      _sleepTimerMinutes = value * 120.0;
                    });
                  },
                ),
              ),
              const SizedBox(height: 16),
              LiquidButton(
                height: 48,
                gradientColors: _sleepTimerEnabled
                    ? AppColors.primaryGradient
                    : [Colors.grey, Colors.grey.shade600],
                onPressed: _sleepTimerEnabled ? _startSleepTimer : null,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.timer, color: Colors.white),
                    const SizedBox(width: 8),
                    Text(
                      'Start Timer (${_sleepTimerMinutes.round()}m)',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(String title, String subtitle, Widget control) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14),
        ),
        const SizedBox(height: 12),
        control,
      ],
    );
  }

  Widget _buildSwitchItem(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: AppColors.liquidCyan,
          activeTrackColor: AppColors.liquidCyan.withOpacity(0.3),
        ),
      ],
    );
  }

  void _resetEqualizer() {
    setState(() {
      _eqBands = List.filled(8, 0.5);
    });
  }

  void _startSleepTimer() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Sleep timer started for ${_sleepTimerMinutes.round()} minutes',
        ),
        backgroundColor: AppColors.liquidBlue,
      ),
    );
  }
}
