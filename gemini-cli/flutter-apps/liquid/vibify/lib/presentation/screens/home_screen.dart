import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/music_library_service.dart';
import '../../core/services/audio_service.dart';
import '../../data/models/song.dart';
import '../../data/models/playlist.dart';
import '../widgets/liquid_container.dart';
import '../widgets/liquid_progress_bar.dart';
import '../widgets/mini_player.dart';
import 'library_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late AnimationController _waveController;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _waveController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat();

    _loadSampleData();
  }

  @override
  void dispose() {
    _floatingController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  void _loadSampleData() {
    // Add some sample songs for demonstration
    final musicLibrary = context.read<MusicLibraryService>();
    final sampleSongs = [
      Song(
        id: '1',
        title: 'Liquid Dreams',
        artist: 'Vibify Artist',
        album: 'Liquid Vibes',
        durationMs: const Duration(minutes: 3, seconds: 45).inMilliseconds,
        filePath: '/sample/path/1.mp3',
        dateAdded: DateTime.now(),
        genre: 'Electronic',
        year: 2024,
      ),
      Song(
        id: '2',
        title: 'Flowing Melodies',
        artist: 'Wave Maker',
        album: 'Ocean Sounds',
        durationMs: const Duration(minutes: 4, seconds: 12).inMilliseconds,
        filePath: '/sample/path/2.mp3',
        dateAdded: DateTime.now().subtract(const Duration(days: 1)),
        genre: 'Ambient',
        year: 2024,
      ),
      Song(
        id: '3',
        title: 'Liquid Motion',
        artist: 'Fluid Beats',
        album: 'Liquid Vibes',
        durationMs: const Duration(minutes: 2, seconds: 58).inMilliseconds,
        filePath: '/sample/path/3.mp3',
        dateAdded: DateTime.now().subtract(const Duration(days: 2)),
        genre: 'Chill',
        year: 2024,
      ),
    ];

    for (final song in sampleSongs) {
      musicLibrary.addSong(song);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(isDark),
      body: Stack(
        children: [
          _buildBody(isDark),
          Positioned(
            left: 0,
            right: 0,
            bottom: 80, // Above bottom nav
            child: const MiniPlayer(),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavBar(isDark),
    );
  }

  PreferredSizeWidget _buildAppBar(bool isDark) {
    return AppBar(
      title: const Text('Vibify', style: AppTextStyles.headlineMedium),
      centerTitle: false,
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            // TODO: Navigate to search screen
          },
        ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () {
            // TODO: Show menu
          },
        ),
      ],
    );
  }

  Widget _buildBody(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark ? AppColors.darkGradient : AppColors.primaryGradient,
        ),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeSection(),
              const SizedBox(height: 24),
              _buildQuickActions(),
              const SizedBox(height: 32),
              _buildRecentlyPlayed(),
              const SizedBox(height: 32),
              _buildFeaturedPlaylists(),
              const SizedBox(height: 32),
              _buildRecentlyAdded(),
              const SizedBox(height: 100), // Space for bottom nav
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return AnimationLimiter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 600),
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: [
            Text(
              'Good ${_getGreeting()}',
              style: AppTextStyles.headlineLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'What would you like to listen to?',
              style: AppTextStyles.bodyLarge.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Morning';
    if (hour < 17) return 'Afternoon';
    return 'Evening';
  }

  Widget _buildQuickActions() {
    return Row(
      children: [
        Expanded(
          child: LiquidContainer(
            height: 120,
            gradientColors: AppColors.secondaryGradient,
            enableGlassmorphism: true,
            ultraTransparent: true,
            blurIntensity: 20.0,
            onTap: () {
              // TODO: Navigate to favorites
            },
            child: const Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.favorite, color: Colors.white, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'Favorites',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: LiquidContainer(
            height: 120,
            gradientColors: AppColors.accentGradient,
            enableGlassmorphism: true,
            ultraTransparent: true,
            blurIntensity: 20.0,
            onTap: () {
              // TODO: Navigate to recently played
            },
            child: const Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.history, color: Colors.white, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'Recent',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecentlyPlayed() {
    return Consumer<MusicLibraryService>(
      builder: (context, musicLibrary, child) {
        final songs = musicLibrary.songs.take(5).toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Recently Played', style: AppTextStyles.sectionHeader),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: songs.length,
                itemBuilder: (context, index) {
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      horizontalOffset: 50.0,
                      child: FadeInAnimation(
                        child: _buildSongCard(songs[index], index),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSongCard(Song song, int index) {
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 16),
      child: LiquidContainer(
        gradientColors: [
          AppColors.waveColors[index % AppColors.waveColors.length],
          AppColors.waveColors[(index + 1) % AppColors.waveColors.length],
        ],
        enableGlassmorphism: true,
        ultraTransparent: true,
        blurIntensity: 25.0,
        onTap: () {
          // TODO: Play song
          final audioService = context.read<AudioPlayerService>();
          audioService.setPlaylist([song]);
          audioService.play();
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.music_note,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                song.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                song.artist,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 12,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedPlaylists() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Featured Playlists', style: AppTextStyles.sectionHeader),
        const SizedBox(height: 16),
        LiquidContainer(
          height: 100,
          gradientColors: AppColors.primaryGradient,
          enableGlassmorphism: true,
          ultraTransparent: true,
          blurIntensity: 30.0,
          child: const Padding(
            padding: EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(Icons.queue_music, color: Colors.white, size: 40),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Discover Weekly',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Your personalized playlist',
                        style: TextStyle(color: Colors.white70, fontSize: 14),
                      ),
                    ],
                  ),
                ),
                Icon(Icons.play_circle_filled, color: Colors.white, size: 32),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecentlyAdded() {
    return Consumer<MusicLibraryService>(
      builder: (context, musicLibrary, child) {
        final songs = musicLibrary.songs.take(3).toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Recently Added', style: AppTextStyles.sectionHeader),
            const SizedBox(height: 16),
            ...songs.asMap().entries.map((entry) {
              final index = entry.key;
              final song = entry.value;
              return AnimationConfiguration.staggeredList(
                position: index,
                duration: const Duration(milliseconds: 375),
                child: SlideAnimation(
                  verticalOffset: 50.0,
                  child: FadeInAnimation(child: _buildSongListItem(song)),
                ),
              );
            }),
          ],
        );
      },
    );
  }

  Widget _buildSongListItem(Song song) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: LiquidContainer(
        height: 80,
        gradientColors: AppColors.glassGradientLight,
        enableGlassmorphism: true,
        ultraTransparent: true,
        blurIntensity: 15.0,
        onTap: () {
          // TODO: Play song
          final audioService = context.read<AudioPlayerService>();
          audioService.setPlaylist([song]);
          audioService.play();
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.liquidBlue.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.music_note,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      song.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${song.artist} • ${song.album}',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Text(
                song.durationString,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
              const SizedBox(width: 8),
              const Icon(Icons.more_vert, color: Colors.white70, size: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavBar(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            (isDark ? AppColors.darkBackground : AppColors.lightBackground)
                .withOpacity(0.9),
          ],
        ),
      ),
      child: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });

          if (index == 1) {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const LibraryScreen()),
            );
          }
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: AppColors.liquidCyan,
        unselectedItemColor: Colors.white.withOpacity(0.6),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.library_music),
            label: 'Library',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.search), label: 'Search'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}
