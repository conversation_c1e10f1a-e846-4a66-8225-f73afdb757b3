import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/music_library_service.dart';
import '../../core/services/audio_service.dart';
import '../../data/models/song.dart';
import '../../data/models/playlist.dart';
import '../widgets/liquid_container.dart';
import '../widgets/liquid_button.dart';

class PlaylistDetailScreen extends StatefulWidget {
  final Playlist playlist;

  const PlaylistDetailScreen({
    super.key,
    required this.playlist,
  });

  @override
  State<PlaylistDetailScreen> createState() => _PlaylistDetailScreenState();
}

class _PlaylistDetailScreenState extends State<PlaylistDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _listController;
  late Animation<double> _headerAnimation;
  late Animation<double> _listAnimation;
  
  bool _isReordering = false;
  List<Song> _songs = [];

  @override
  void initState() {
    super.initState();
    
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _listController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _headerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOutCubic,
    ));

    _listAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _listController,
      curve: Curves.easeOutCubic,
    ));

    _headerController.forward();
    _listController.forward();
    _loadSongs();
  }

  @override
  void dispose() {
    _headerController.dispose();
    _listController.dispose();
    super.dispose();
  }

  void _loadSongs() {
    final musicLibrary = context.read<MusicLibraryService>();
    setState(() {
      _songs = widget.playlist.songIds
          .map((id) => musicLibrary.getSong(id))
          .where((song) => song != null)
          .cast<Song>()
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isReordering ? Icons.done : Icons.edit,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                _isReordering = !_isReordering;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onPressed: _showPlaylistOptions,
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isDark ? AppColors.darkGradient : AppColors.primaryGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _buildSongsList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return AnimatedBuilder(
      animation: _headerAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - _headerAnimation.value)),
          child: Opacity(
            opacity: _headerAnimation.value,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: LiquidContainer(
                height: 200,
                gradientColors: AppColors.secondaryGradient,
                enableGlassmorphism: true,
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              color: Colors.white.withOpacity(0.2),
                            ),
                            child: Icon(
                              widget.playlist.type == PlaylistType.favorites
                                  ? Icons.favorite
                                  : Icons.queue_music,
                              color: Colors.white,
                              size: 40,
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.playlist.name,
                                  style: AppTextStyles.headlineMedium.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  '${_songs.length} songs',
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: Colors.white.withOpacity(0.8),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      Row(
                        children: [
                          Expanded(
                            child: LiquidButton(
                              height: 48,
                              gradientColors: AppColors.accentGradient,
                              onPressed: _playPlaylist,
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.play_arrow, color: Colors.white),
                                  SizedBox(width: 8),
                                  Text(
                                    'Play All',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          LiquidButton(
                            width: 48,
                            height: 48,
                            isCircular: true,
                            gradientColors: [
                              Colors.white.withOpacity(0.2),
                              Colors.white.withOpacity(0.1),
                            ],
                            onPressed: _shufflePlaylist,
                            child: const Icon(
                              Icons.shuffle,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSongsList() {
    if (_songs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_off,
              size: 64,
              color: Colors.white.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No songs in this playlist',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 24),
            LiquidButton(
              gradientColors: AppColors.primaryGradient,
              onPressed: _addSongs,
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.add, color: Colors.white),
                  SizedBox(width: 8),
                  Text(
                    'Add Songs',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return AnimatedBuilder(
      animation: _listAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _listAnimation.value)),
          child: Opacity(
            opacity: _listAnimation.value,
            child: _isReordering ? _buildReorderableList() : _buildNormalList(),
          ),
        );
      },
    );
  }

  Widget _buildNormalList() {
    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _songs.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildSongItem(_songs[index], index),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildReorderableList() {
    return ReorderableListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _songs.length,
      onReorder: _onReorder,
      proxyDecorator: (child, index, animation) {
        return AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            return Transform.scale(
              scale: 1.05,
              child: Transform.rotate(
                angle: animation.value * 0.1,
                child: Material(
                  color: Colors.transparent,
                  child: LiquidContainer(
                    height: 80,
                    gradientColors: AppColors.accentGradient,
                    child: child!,
                  ),
                ),
              ),
            );
          },
          child: child,
        );
      },
      itemBuilder: (context, index) {
        return _buildSongItem(_songs[index], index, key: ValueKey(_songs[index].id));
      },
    );
  }

  Widget _buildSongItem(Song song, int index, {Key? key}) {
    return Container(
      key: key,
      margin: const EdgeInsets.only(bottom: 12),
      child: LiquidContainer(
        height: 80,
        gradientColors: [
          AppColors.waveColors[index % AppColors.waveColors.length]
              .withOpacity(0.3),
          AppColors.waveColors[(index + 1) % AppColors.waveColors.length]
              .withOpacity(0.1),
        ],
        onTap: _isReordering ? null : () => _playSong(index),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              if (_isReordering)
                const Icon(
                  Icons.drag_handle,
                  color: Colors.white70,
                  size: 20,
                )
              else
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    gradient: LinearGradient(
                      colors: [
                        AppColors.waveColors[index % AppColors.waveColors.length],
                        AppColors.waveColors[(index + 1) % AppColors.waveColors.length],
                      ],
                    ),
                  ),
                  child: const Icon(
                    Icons.music_note,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      song.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${song.artist} • ${song.album ?? 'Unknown Album'}',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              if (!_isReordering) ...[
                Text(
                  song.durationString,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(
                    Icons.more_vert,
                    color: Colors.white70,
                    size: 20,
                  ),
                  onPressed: () => _showSongOptions(song, index),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _onReorder(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    
    setState(() {
      final song = _songs.removeAt(oldIndex);
      _songs.insert(newIndex, song);
    });

    // Update playlist in database
    final musicLibrary = context.read<MusicLibraryService>();
    final updatedPlaylist = widget.playlist.copyWith(
      songIds: _songs.map((song) => song.id).toList(),
      updatedAt: DateTime.now(),
    );
    musicLibrary.updatePlaylist(updatedPlaylist);
  }

  void _playPlaylist() {
    if (_songs.isNotEmpty) {
      final audioService = context.read<AudioPlayerService>();
      audioService.setPlaylist(_songs);
      audioService.play();
    }
  }

  void _shufflePlaylist() {
    if (_songs.isNotEmpty) {
      final audioService = context.read<AudioPlayerService>();
      audioService.setPlaylist(_songs);
      audioService.setShuffleMode(ShuffleMode.on);
      audioService.play();
    }
  }

  void _playSong(int index) {
    final audioService = context.read<AudioPlayerService>();
    audioService.setPlaylist(_songs, initialIndex: index);
    audioService.play();
  }

  void _addSongs() {
    // TODO: Navigate to song selection screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add songs feature coming soon!'),
        backgroundColor: AppColors.liquidBlue,
      ),
    );
  }

  void _showPlaylistOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildOptionsBottomSheet(),
    );
  }

  void _showSongOptions(Song song, int index) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSongOptionsBottomSheet(song, index),
    );
  }

  Widget _buildOptionsBottomSheet() {
    return LiquidContainer(
      height: 300,
      gradientColors: AppColors.darkGradient,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Playlist Options',
              style: AppTextStyles.headlineSmall.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 24),
            _buildOptionItem(
              icon: Icons.edit,
              title: 'Edit Playlist',
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to edit playlist screen
              },
            ),
            _buildOptionItem(
              icon: Icons.share,
              title: 'Share Playlist',
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement share functionality
              },
            ),
            _buildOptionItem(
              icon: Icons.delete,
              title: 'Delete Playlist',
              onTap: () {
                Navigator.pop(context);
                _deletePlaylist();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSongOptionsBottomSheet(Song song, int index) {
    return LiquidContainer(
      height: 250,
      gradientColors: AppColors.darkGradient,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Song Options',
              style: AppTextStyles.headlineSmall.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 24),
            _buildOptionItem(
              icon: Icons.play_arrow,
              title: 'Play Next',
              onTap: () {
                Navigator.pop(context);
                // TODO: Add to queue
              },
            ),
            _buildOptionItem(
              icon: Icons.playlist_add,
              title: 'Add to Playlist',
              onTap: () {
                Navigator.pop(context);
                // TODO: Show playlist selection
              },
            ),
            _buildOptionItem(
              icon: Icons.remove,
              title: 'Remove from Playlist',
              onTap: () {
                Navigator.pop(context);
                _removeSongFromPlaylist(song, index);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(width: 16),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _removeSongFromPlaylist(Song song, int index) {
    setState(() {
      _songs.removeAt(index);
    });

    final musicLibrary = context.read<MusicLibraryService>();
    musicLibrary.removeSongFromPlaylist(widget.playlist.id, song.id);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Removed "${song.title}" from playlist'),
        backgroundColor: AppColors.liquidBlue,
        action: SnackBarAction(
          label: 'Undo',
          textColor: Colors.white,
          onPressed: () {
            setState(() {
              _songs.insert(index, song);
            });
            musicLibrary.addSongToPlaylist(widget.playlist.id, song.id);
          },
        ),
      ),
    );
  }

  void _deletePlaylist() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.darkSurface,
        title: const Text(
          'Delete Playlist',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to delete "${widget.playlist.name}"?',
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              final musicLibrary = context.read<MusicLibraryService>();
              musicLibrary.deletePlaylist(widget.playlist.id);
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
