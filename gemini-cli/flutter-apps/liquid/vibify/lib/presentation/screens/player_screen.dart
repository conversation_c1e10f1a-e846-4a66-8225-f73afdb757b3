import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/audio_service.dart';
import '../../data/models/song.dart';
import '../widgets/liquid_container.dart';
import '../widgets/liquid_progress_bar.dart';
import 'settings_screen.dart';

class PlayerScreen extends StatefulWidget {
  const PlayerScreen({super.key});

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _waveController;
  late AnimationController _pulseController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _waveAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _waveController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_rotationController);

    _waveAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _waveController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.keyboard_arrow_down, size: 32),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showPlayerOptions(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isDark ? AppColors.darkGradient : AppColors.primaryGradient,
          ),
        ),
        child: SafeArea(
          child: Consumer<AudioPlayerService>(
            builder: (context, audioService, child) {
              return StreamBuilder<Song?>(
                stream: audioService.currentSongStream,
                builder: (context, snapshot) {
                  final currentSong = snapshot.data;

                  if (currentSong == null) {
                    return const Center(
                      child: Text(
                        'No song playing',
                        style: TextStyle(color: Colors.white, fontSize: 18),
                      ),
                    );
                  }

                  return Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        const SizedBox(height: 20),

                        // Album artwork with liquid effects
                        Expanded(
                          flex: 3,
                          child: _buildAlbumArtwork(currentSong),
                        ),

                        const SizedBox(height: 40),

                        // Song info
                        _buildSongInfo(currentSong),

                        const SizedBox(height: 32),

                        // Progress bar
                        _buildProgressBar(audioService),

                        const SizedBox(height: 32),

                        // Control buttons
                        _buildControlButtons(audioService),

                        const SizedBox(height: 24),

                        // Volume control
                        _buildVolumeControl(audioService),

                        const SizedBox(height: 20),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArtwork(Song song) {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Animated background waves
          AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              return CustomPaint(
                painter: LiquidWavePainter(
                  animation: _waveAnimation,
                  colors: AppColors.waveColors,
                ),
                size: const Size(300, 300),
              );
            },
          ),

          // Pulsing outer ring
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 280,
                  height: 280,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: AppColors.secondaryGradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.liquidCyan.withOpacity(0.3),
                        blurRadius: 30,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          // Rotating album art
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value * 2 * 3.14159,
                child: LiquidContainer(
                  width: 240,
                  height: 240,
                  gradientColors: AppColors.accentGradient,
                  child: Container(
                    decoration: const BoxDecoration(shape: BoxShape.circle),
                    child: const Icon(
                      Icons.music_note,
                      color: Colors.white,
                      size: 80,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSongInfo(Song song) {
    return Column(
      children: [
        Text(
          song.title,
          style: AppTextStyles.headlineMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          song.artist,
          style: AppTextStyles.titleMedium.copyWith(
            color: Colors.white.withOpacity(0.8),
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (song.album != null) ...[
          const SizedBox(height: 4),
          Text(
            song.album!,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildProgressBar(AudioPlayerService audioService) {
    return StreamBuilder<Duration>(
      stream: audioService.positionStream,
      builder: (context, positionSnapshot) {
        return StreamBuilder<Duration>(
          stream: audioService.durationStream,
          builder: (context, durationSnapshot) {
            final position = positionSnapshot.data ?? Duration.zero;
            final duration = durationSnapshot.data ?? Duration.zero;
            final progress = duration.inMilliseconds > 0
                ? position.inMilliseconds / duration.inMilliseconds
                : 0.0;

            return Column(
              children: [
                LiquidProgressBar(
                  value: progress,
                  height: 6,
                  isInteractive: true,
                  showBubbles: true,
                  gradientColors: [
                    Colors.white.withOpacity(0.9),
                    AppColors.liquidCyan.withOpacity(0.8),
                  ],
                  onChanged: (value) {
                    final newPosition = Duration(
                      milliseconds: (value * duration.inMilliseconds).round(),
                    );
                    audioService.seek(newPosition);
                  },
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(position),
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                        fontFeatures: const [FontFeature.tabularFigures()],
                      ),
                    ),
                    Text(
                      _formatDuration(duration),
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                        fontFeatures: const [FontFeature.tabularFigures()],
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildControlButtons(AudioPlayerService audioService) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Shuffle
        StreamBuilder<ShuffleMode>(
          stream: audioService.shuffleModeStream,
          builder: (context, snapshot) {
            final isShuffleOn = snapshot.data == ShuffleMode.on;
            return _buildControlButton(
              icon: Icons.shuffle,
              isActive: isShuffleOn,
              onTap: () => audioService.setShuffleMode(
                isShuffleOn ? ShuffleMode.off : ShuffleMode.on,
              ),
            );
          },
        ),

        // Previous
        _buildControlButton(
          icon: Icons.skip_previous,
          size: 32,
          onTap: () => audioService.skipToPrevious(),
        ),

        // Play/Pause
        StreamBuilder<PlaybackState>(
          stream: audioService.playbackStateStream,
          builder: (context, snapshot) {
            final isPlaying = snapshot.data == PlaybackState.playing;
            final isLoading = snapshot.data == PlaybackState.buffering;

            return LiquidContainer(
              width: 80,
              height: 80,
              gradientColors: AppColors.secondaryGradient,
              onTap: () {
                if (isPlaying) {
                  audioService.pause();
                } else {
                  audioService.play();
                }
              },
              child: isLoading
                  ? const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    )
                  : Icon(
                      isPlaying ? Icons.pause : Icons.play_arrow,
                      color: Colors.white,
                      size: 40,
                    ),
            );
          },
        ),

        // Next
        _buildControlButton(
          icon: Icons.skip_next,
          size: 32,
          onTap: () => audioService.skipToNext(),
        ),

        // Repeat
        StreamBuilder<RepeatMode>(
          stream: audioService.repeatModeStream,
          builder: (context, snapshot) {
            final repeatMode = snapshot.data ?? RepeatMode.off;
            return _buildControlButton(
              icon: repeatMode == RepeatMode.one
                  ? Icons.repeat_one
                  : Icons.repeat,
              isActive: repeatMode != RepeatMode.off,
              onTap: () {
                switch (repeatMode) {
                  case RepeatMode.off:
                    audioService.setRepeatMode(RepeatMode.all);
                    break;
                  case RepeatMode.all:
                    audioService.setRepeatMode(RepeatMode.one);
                    break;
                  case RepeatMode.one:
                    audioService.setRepeatMode(RepeatMode.off);
                    break;
                }
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    double size = 24,
    bool isActive = false,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isActive
              ? Colors.white.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
        ),
        child: Icon(
          icon,
          color: isActive ? Colors.white : Colors.white.withOpacity(0.8),
          size: size,
        ),
      ),
    );
  }

  Widget _buildVolumeControl(AudioPlayerService audioService) {
    return StreamBuilder<double>(
      stream: audioService.volumeStream,
      builder: (context, snapshot) {
        final volume = snapshot.data ?? 1.0;

        return Row(
          children: [
            Icon(
              Icons.volume_down,
              color: Colors.white.withOpacity(0.8),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: LiquidProgressBar(
                value: volume,
                height: 4,
                isInteractive: true,
                showBubbles: false,
                gradientColors: [
                  Colors.white.withOpacity(0.6),
                  Colors.white.withOpacity(0.9),
                ],
                onChanged: (value) => audioService.setVolume(value),
              ),
            ),
            const SizedBox(width: 12),
            Icon(
              Icons.volume_up,
              color: Colors.white.withOpacity(0.8),
              size: 20,
            ),
          ],
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void _showPlayerOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => LiquidContainer(
        height: 250,
        gradientColors: AppColors.darkGradient,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Player Options',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),
              _buildOptionItem(
                icon: Icons.equalizer,
                title: 'Audio Settings',
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const SettingsScreen(),
                    ),
                  );
                },
              ),
              _buildOptionItem(
                icon: Icons.queue_music,
                title: 'Queue',
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Show queue
                },
              ),
              _buildOptionItem(
                icon: Icons.share,
                title: 'Share Song',
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Share functionality
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(width: 16),
            Text(
              title,
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}

class LiquidWavePainter extends CustomPainter {
  final Animation<double> animation;
  final List<Color> colors;

  LiquidWavePainter({required this.animation, required this.colors});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    for (int i = 0; i < 3; i++) {
      final paint = Paint()
        ..color = colors[i % colors.length].withOpacity(0.1)
        ..style = PaintingStyle.fill;

      final waveRadius =
          radius *
          (0.5 + 0.3 * i) *
          (1 + 0.1 * (animation.value + i * 0.3) % 1);

      canvas.drawCircle(center, waveRadius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
