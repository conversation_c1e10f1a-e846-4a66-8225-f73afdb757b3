import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../core/theme/app_colors.dart';

class FloatingParticles extends StatefulWidget {
  final int particleCount;
  final double minSize;
  final double maxSize;
  final Duration animationDuration;
  final List<Color> colors;

  const FloatingParticles({
    super.key,
    this.particleCount = 20,
    this.minSize = 2.0,
    this.maxSize = 8.0,
    this.animationDuration = const Duration(seconds: 10),
    this.colors = AppColors.waveColors,
  });

  @override
  State<FloatingParticles> createState() => _FloatingParticlesState();
}

class _FloatingParticlesState extends State<FloatingParticles>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    )..repeat();

    _generateParticles();
  }

  void _generateParticles() {
    final random = math.Random();
    _particles = List.generate(widget.particleCount, (index) {
      return Particle(
        x: random.nextDouble(),
        y: random.nextDouble(),
        size: widget.minSize + random.nextDouble() * (widget.maxSize - widget.minSize),
        color: widget.colors[random.nextInt(widget.colors.length)],
        speed: 0.1 + random.nextDouble() * 0.3,
        direction: random.nextDouble() * 2 * math.pi,
        opacity: 0.1 + random.nextDouble() * 0.4,
        pulseSpeed: 0.5 + random.nextDouble() * 1.5,
      );
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          painter: ParticlesPainter(
            particles: _particles,
            animation: _controller,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

class Particle {
  double x;
  double y;
  final double size;
  final Color color;
  final double speed;
  final double direction;
  final double opacity;
  final double pulseSpeed;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.color,
    required this.speed,
    required this.direction,
    required this.opacity,
    required this.pulseSpeed,
  });
}

class ParticlesPainter extends CustomPainter {
  final List<Particle> particles;
  final Animation<double> animation;

  ParticlesPainter({
    required this.particles,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (final particle in particles) {
      // Update particle position
      particle.x += math.cos(particle.direction) * particle.speed * 0.001;
      particle.y += math.sin(particle.direction) * particle.speed * 0.001;

      // Wrap around screen edges
      if (particle.x > 1.0) particle.x = 0.0;
      if (particle.x < 0.0) particle.x = 1.0;
      if (particle.y > 1.0) particle.y = 0.0;
      if (particle.y < 0.0) particle.y = 1.0;

      // Calculate pulsing opacity
      final pulseOpacity = particle.opacity * 
          (0.5 + 0.5 * math.sin(animation.value * 2 * math.pi * particle.pulseSpeed));

      // Draw particle
      final paint = Paint()
        ..color = particle.color.withOpacity(pulseOpacity)
        ..style = PaintingStyle.fill;

      final center = Offset(
        particle.x * size.width,
        particle.y * size.height,
      );

      // Draw particle with glow effect
      final glowPaint = Paint()
        ..color = particle.color.withOpacity(pulseOpacity * 0.3)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

      canvas.drawCircle(center, particle.size * 2, glowPaint);
      canvas.drawCircle(center, particle.size, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Enhanced background with floating particles and liquid waves
class LiquidBackground extends StatefulWidget {
  final Widget child;
  final List<Color> gradientColors;
  final bool showParticles;
  final bool showWaves;

  const LiquidBackground({
    super.key,
    required this.child,
    this.gradientColors = AppColors.primaryGradient,
    this.showParticles = true,
    this.showWaves = true,
  });

  @override
  State<LiquidBackground> createState() => _LiquidBackgroundState();
}

class _LiquidBackgroundState extends State<LiquidBackground>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _particleController;

  @override
  void initState() {
    super.initState();
    
    _waveController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();

    _particleController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _waveController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: widget.gradientColors,
        ),
      ),
      child: Stack(
        children: [
          // Animated liquid waves
          if (widget.showWaves)
            AnimatedBuilder(
              animation: _waveController,
              builder: (context, child) {
                return CustomPaint(
                  painter: LiquidWavesPainter(
                    animation: _waveController,
                    colors: AppColors.waveColors,
                  ),
                  size: Size.infinite,
                );
              },
            ),
          
          // Floating particles
          if (widget.showParticles)
            FloatingParticles(
              particleCount: 25,
              minSize: 1.5,
              maxSize: 6.0,
              animationDuration: const Duration(seconds: 12),
              colors: AppColors.waveColors,
            ),
          
          // Main content
          widget.child,
        ],
      ),
    );
  }
}

class LiquidWavesPainter extends CustomPainter {
  final Animation<double> animation;
  final List<Color> colors;

  LiquidWavesPainter({
    required this.animation,
    required this.colors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw multiple liquid waves
    for (int i = 0; i < 4; i++) {
      final paint = Paint()
        ..color = colors[i % colors.length].withOpacity(0.03)
        ..style = PaintingStyle.fill;

      final radius = (size.width * 0.6) * (0.4 + 0.3 * i) * 
          (1 + 0.2 * math.sin(animation.value * 2 * math.pi + i * 0.8));

      // Create organic wave shape
      final path = Path();
      const segments = 12;
      final angleStep = 2 * math.pi / segments;
      
      for (int j = 0; j <= segments; j++) {
        final angle = j * angleStep + animation.value * math.pi * (i % 2 == 0 ? 1 : -1);
        final waveRadius = radius + 
            30 * math.sin(angle * 2 + animation.value * 3 * math.pi) +
            15 * math.sin(angle * 4 + animation.value * 5 * math.pi);
        
        final x = center.dx + waveRadius * math.cos(angle);
        final y = center.dy + waveRadius * math.sin(angle);
        
        if (j == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      
      path.close();
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
