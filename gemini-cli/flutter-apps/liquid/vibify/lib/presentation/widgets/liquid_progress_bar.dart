import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:liquid_progress_indicator/liquid_progress_indicator.dart';
import '../../core/theme/app_colors.dart';

class LiquidProgressBar extends StatefulWidget {
  final double value;
  final double height;
  final Color? backgroundColor;
  final List<Color>? gradientColors;
  final Function(double)? onChanged;
  final bool isInteractive;
  final bool showBubbles;

  const LiquidProgressBar({
    super.key,
    required this.value,
    this.height = 6.0,
    this.backgroundColor,
    this.gradientColors,
    this.onChanged,
    this.isInteractive = false,
    this.showBubbles = true,
  });

  @override
  State<LiquidProgressBar> createState() => _LiquidProgressBarState();
}

class _LiquidProgressBarState extends State<LiquidProgressBar>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _bubbleController;
  double _currentValue = 0.0;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.value;

    _waveController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _bubbleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
  }

  @override
  void didUpdateWidget(LiquidProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      setState(() {
        _currentValue = widget.value;
      });
    }
  }

  @override
  void dispose() {
    _waveController.dispose();
    _bubbleController.dispose();
    super.dispose();
  }

  void _handlePanUpdate(DragUpdateDetails details, BoxConstraints constraints) {
    if (!widget.isInteractive || widget.onChanged == null) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    final progress = (localPosition.dx / constraints.maxWidth).clamp(0.0, 1.0);

    setState(() {
      _currentValue = progress;
    });

    widget.onChanged!(progress);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final gradientColors =
        widget.gradientColors ??
        (isDark ? AppColors.secondaryGradient : AppColors.primaryGradient);
    final backgroundColor =
        widget.backgroundColor ??
        (isDark ? AppColors.darkCardBackground : AppColors.lightCardBackground);

    return LayoutBuilder(
      builder: (context, constraints) {
        return GestureDetector(
          onPanUpdate: (details) => _handlePanUpdate(details, constraints),
          onTapDown: widget.isInteractive
              ? (details) {
                  final RenderBox renderBox =
                      context.findRenderObject() as RenderBox;
                  final localPosition = renderBox.globalToLocal(
                    details.globalPosition,
                  );
                  final progress = (localPosition.dx / constraints.maxWidth)
                      .clamp(0.0, 1.0);

                  setState(() {
                    _currentValue = progress;
                  });

                  widget.onChanged?.call(progress);
                }
              : null,
          child: Container(
            height: widget.height,
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(widget.height / 2),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(widget.height / 2),
              child: Stack(
                children: [
                  // Background
                  Container(
                    width: double.infinity,
                    height: widget.height,
                    color: backgroundColor,
                  ),

                  // Liquid progress
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: constraints.maxWidth * _currentValue,
                    height: widget.height,
                    child: LiquidLinearProgressIndicator(
                      value: 1.0, // Always full within its container
                      valueColor: AlwaysStoppedAnimation(gradientColors.first),
                      backgroundColor: Colors.transparent,
                      borderColor: Colors.transparent,
                      borderWidth: 0,
                      borderRadius: widget.height / 2,
                      direction: Axis.horizontal,
                      center: widget.showBubbles
                          ? AnimatedBuilder(
                              animation: _bubbleController,
                              builder: (context, child) {
                                return CustomPaint(
                                  painter: BubblePainter(
                                    animation: _bubbleController,
                                    color: Colors.white.withOpacity(0.3),
                                  ),
                                  size: Size(
                                    constraints.maxWidth * _currentValue,
                                    widget.height,
                                  ),
                                );
                              },
                            )
                          : null,
                    ),
                  ),

                  // Wave effect overlay
                  if (widget.showBubbles)
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      width: constraints.maxWidth * _currentValue,
                      height: widget.height,
                      child: AnimatedBuilder(
                        animation: _waveController,
                        builder: (context, child) {
                          return CustomPaint(
                            painter: WavePainter(
                              animation: _waveController,
                              color: gradientColors.last.withOpacity(0.5),
                            ),
                            size: Size(
                              constraints.maxWidth * _currentValue,
                              widget.height,
                            ),
                          );
                        },
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class BubblePainter extends CustomPainter {
  final Animation<double> animation;
  final Color color;

  BubblePainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Draw floating bubbles
    for (int i = 0; i < 5; i++) {
      final x = (size.width * 0.2 * i) + (animation.value * 20);
      final y =
          size.height * 0.5 +
          (10 * (i % 2 == 0 ? 1 : -1)) *
              (0.5 + 0.5 * (animation.value + i * 0.2) % 1);
      final radius = 2.0 + (animation.value * 2);

      canvas.drawCircle(Offset(x % size.width, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class WavePainter extends CustomPainter {
  final Animation<double> animation;
  final Color color;

  WavePainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = size.height * 0.3;
    final waveLength = size.width * 0.5;

    path.moveTo(0, size.height * 0.5);

    for (double x = 0; x <= size.width; x += 1) {
      final y =
          size.height * 0.5 +
          waveHeight *
              math.sin(
                animation.value * 2 * math.pi + x / waveLength * 2 * math.pi,
              );
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
