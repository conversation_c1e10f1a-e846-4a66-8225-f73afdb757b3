import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'core/theme/app_theme.dart';
import 'core/services/audio_service.dart';
import 'core/services/music_library_service.dart';
import 'core/utils/duration_adapter.dart';
import 'presentation/screens/home_screen.dart';
import 'data/models/song.dart';
import 'data/models/playlist.dart';
import 'data/models/album.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(DurationAdapter());
  Hive.registerAdapter(SongAdapter());
  Hive.registerAdapter(PlaylistAdapter());
  Hive.registerAdapter(PlaylistTypeAdapter());
  Hive.registerAdapter(AlbumAdapter());

  // Initialize services
  await AudioPlayerService().initialize();
  await MusicLibraryService().initialize();

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.transparent,
    ),
  );

  runApp(const VibifyApp());
}

class VibifyApp extends StatelessWidget {
  const VibifyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<AudioPlayerService>.value(value: AudioPlayerService()),
        Provider<MusicLibraryService>.value(value: MusicLibraryService()),
      ],
      child: MaterialApp(
        title: 'Vibify',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const HomeScreen(),
      ),
    );
  }
}
