import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

class AnimationUtils {
  // Reduce animation complexity on lower-end devices
  static bool get shouldUseReducedAnimations {
    // In debug mode, always use full animations for testing
    if (kDebugMode) return false;
    
    // You could add device performance detection here
    // For now, we'll use a simple heuristic
    return false;
  }

  // Get appropriate animation duration based on device performance
  static Duration getAnimationDuration(Duration defaultDuration) {
    if (shouldUseReducedAnimations) {
      return Duration(milliseconds: (defaultDuration.inMilliseconds * 0.7).round());
    }
    return defaultDuration;
  }

  // Get appropriate curve based on device performance
  static Curve getAnimationCurve(Curve defaultCurve) {
    if (shouldUseReducedAnimations) {
      return Curves.easeInOut; // Simpler curve for better performance
    }
    return defaultCurve;
  }

  // Optimized liquid wave animation controller
  static AnimationController createOptimizedController({
    required TickerProvider vsync,
    Duration duration = const Duration(seconds: 2),
    bool shouldRepeat = true,
  }) {
    final optimizedDuration = getAnimationDuration(duration);
    final controller = AnimationController(
      duration: optimizedDuration,
      vsync: vsync,
    );

    if (shouldRepeat) {
      controller.repeat();
    }

    return controller;
  }

  // Create optimized animation with automatic disposal
  static Animation<T> createOptimizedAnimation<T>({
    required AnimationController controller,
    required Tween<T> tween,
    Curve curve = Curves.easeInOut,
  }) {
    final optimizedCurve = getAnimationCurve(curve);
    return tween.animate(CurvedAnimation(
      parent: controller,
      curve: optimizedCurve,
    ));
  }

  // Debounced animation trigger to prevent excessive animations
  static void debounceAnimation(
    VoidCallback callback, {
    Duration delay = const Duration(milliseconds: 100),
  }) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  static Timer? _debounceTimer;

  // Dispose debounce timer
  static void dispose() {
    _debounceTimer?.cancel();
    _debounceTimer = null;
  }
}

// Optimized liquid animation widget
class OptimizedLiquidAnimation extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final bool enableAnimation;
  final Curve curve;

  const OptimizedLiquidAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 2),
    this.enableAnimation = true,
    this.curve = Curves.easeInOut,
  });

  @override
  State<OptimizedLiquidAnimation> createState() => _OptimizedLiquidAnimationState();
}

class _OptimizedLiquidAnimationState extends State<OptimizedLiquidAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    if (widget.enableAnimation && !AnimationUtils.shouldUseReducedAnimations) {
      _controller = AnimationUtils.createOptimizedController(
        vsync: this,
        duration: widget.duration,
      );
      
      _animation = AnimationUtils.createOptimizedAnimation(
        controller: _controller,
        tween: Tween<double>(begin: 0.0, end: 1.0),
        curve: widget.curve,
      );
    } else {
      // Create a dummy controller for non-animated state
      _controller = AnimationController(
        duration: Duration.zero,
        vsync: this,
      );
      _animation = AlwaysStoppedAnimation(1.0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enableAnimation || AnimationUtils.shouldUseReducedAnimations) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) => widget.child,
    );
  }
}

// Performance-aware RepaintBoundary wrapper
class OptimizedRepaintBoundary extends StatelessWidget {
  final Widget child;
  final bool forceRepaintBoundary;

  const OptimizedRepaintBoundary({
    super.key,
    required this.child,
    this.forceRepaintBoundary = false,
  });

  @override
  Widget build(BuildContext context) {
    // Only use RepaintBoundary for complex widgets or when forced
    if (forceRepaintBoundary || !AnimationUtils.shouldUseReducedAnimations) {
      return RepaintBoundary(child: child);
    }
    return child;
  }
}

// Optimized custom painter for liquid effects
abstract class OptimizedLiquidPainter extends CustomPainter {
  final Animation<double> animation;
  final bool enableComplexEffects;

  OptimizedLiquidPainter({
    required this.animation,
    this.enableComplexEffects = true,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    if (AnimationUtils.shouldUseReducedAnimations) {
      paintSimplified(canvas, size);
    } else if (enableComplexEffects) {
      paintComplex(canvas, size);
    } else {
      paintSimplified(canvas, size);
    }
  }

  // Complex painting for high-performance devices
  void paintComplex(Canvas canvas, Size size);

  // Simplified painting for lower-performance devices
  void paintSimplified(Canvas canvas, Size size);

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return !AnimationUtils.shouldUseReducedAnimations;
  }
}

// Memory-efficient animation cache
class AnimationCache {
  static final Map<String, Animation> _cache = {};
  static const int maxCacheSize = 50;

  static Animation<T>? get<T>(String key) {
    return _cache[key] as Animation<T>?;
  }

  static void put<T>(String key, Animation<T> animation) {
    if (_cache.length >= maxCacheSize) {
      // Remove oldest entry
      final firstKey = _cache.keys.first;
      _cache.remove(firstKey);
    }
    _cache[key] = animation;
  }

  static void clear() {
    _cache.clear();
  }
}

// Timer import for debouncing
import 'dart:async';
