import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';

class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final List<double> _frameTimes = [];
  double _averageFrameTime = 0.0;
  int _droppedFrames = 0;
  bool _isMonitoring = false;

  void startMonitoring() {
    if (_isMonitoring || !kDebugMode) return;
    
    _isMonitoring = true;
    SchedulerBinding.instance.addTimingsCallback(_onFrameTimings);
  }

  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    SchedulerBinding.instance.removeTimingsCallback(_onFrameTimings);
  }

  void _onFrameTimings(List<FrameTiming> timings) {
    for (final timing in timings) {
      final frameTime = timing.totalSpan.inMicroseconds / 1000.0; // Convert to milliseconds
      _frameTimes.add(frameTime);
      
      // Keep only the last 60 frames for average calculation
      if (_frameTimes.length > 60) {
        _frameTimes.removeAt(0);
      }
      
      // Calculate average frame time
      _averageFrameTime = _frameTimes.reduce((a, b) => a + b) / _frameTimes.length;
      
      // Count dropped frames (frames taking longer than 16.67ms for 60fps)
      if (frameTime > 16.67) {
        _droppedFrames++;
      }
    }
  }

  double get averageFrameTime => _averageFrameTime;
  double get fps => _averageFrameTime > 0 ? 1000.0 / _averageFrameTime : 0.0;
  int get droppedFrames => _droppedFrames;
  bool get isPerformanceGood => fps >= 55.0; // Consider 55+ fps as good performance

  void reset() {
    _frameTimes.clear();
    _averageFrameTime = 0.0;
    _droppedFrames = 0;
  }

  void logPerformance() {
    if (kDebugMode) {
      debugPrint('Performance Monitor:');
      debugPrint('  Average FPS: ${fps.toStringAsFixed(1)}');
      debugPrint('  Average Frame Time: ${_averageFrameTime.toStringAsFixed(2)}ms');
      debugPrint('  Dropped Frames: $_droppedFrames');
      debugPrint('  Performance Status: ${isPerformanceGood ? "Good" : "Poor"}');
    }
  }
}

// Widget to display performance metrics in debug mode
class PerformanceOverlay extends StatefulWidget {
  final Widget child;
  final bool showOverlay;

  const PerformanceOverlay({
    super.key,
    required this.child,
    this.showOverlay = kDebugMode,
  });

  @override
  State<PerformanceOverlay> createState() => _PerformanceOverlayState();
}

class _PerformanceOverlayState extends State<PerformanceOverlay>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  final PerformanceMonitor _monitor = PerformanceMonitor();

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat();
    
    if (widget.showOverlay) {
      _monitor.startMonitoring();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _monitor.stopMonitoring();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showOverlay) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: MediaQuery.of(context).padding.top + 10,
          right: 10,
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'FPS: ${_monitor.fps.toStringAsFixed(1)}',
                      style: TextStyle(
                        color: _monitor.isPerformanceGood ? Colors.green : Colors.red,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Frame: ${_monitor.averageFrameTime.toStringAsFixed(1)}ms',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                    Text(
                      'Dropped: ${_monitor.droppedFrames}',
                      style: const TextStyle(
                        color: Colors.orange,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
