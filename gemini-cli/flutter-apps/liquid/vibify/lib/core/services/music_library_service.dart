import 'dart:async';
import 'dart:io';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import '../../data/models/song.dart';
import '../../data/models/playlist.dart';
import '../../data/models/album.dart';

class MusicLibraryService {
  static final MusicLibraryService _instance = MusicLibraryService._internal();
  factory MusicLibraryService() => _instance;
  MusicLibraryService._internal();

  late Box<Song> _songsBox;
  late Box<Playlist> _playlistsBox;
  late Box<Album> _albumsBox;

  final StreamController<List<Song>> _songsController = 
      StreamController<List<Song>>.broadcast();
  final StreamController<List<Playlist>> _playlistsController = 
      StreamController<List<Playlist>>.broadcast();
  final StreamController<List<Album>> _albumsController = 
      StreamController<List<Album>>.broadcast();

  // Getters
  Stream<List<Song>> get songsStream => _songsController.stream;
  Stream<List<Playlist>> get playlistsStream => _playlistsController.stream;
  Stream<List<Album>> get albumsStream => _albumsController.stream;

  List<Song> get songs => _songsBox.values.toList();
  List<Playlist> get playlists => _playlistsBox.values.toList();
  List<Album> get albums => _albumsBox.values.toList();

  Future<void> initialize() async {
    try {
      final appDocumentDir = await getApplicationDocumentsDirectory();
      Hive.init(appDocumentDir.path);

      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(SongAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(PlaylistAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(PlaylistTypeAdapter());
      }
      if (!Hive.isAdapterRegistered(3)) {
        Hive.registerAdapter(AlbumAdapter());
      }

      // Open boxes
      _songsBox = await Hive.openBox<Song>('songs');
      _playlistsBox = await Hive.openBox<Playlist>('playlists');
      _albumsBox = await Hive.openBox<Album>('albums');

      // Create default playlists if they don't exist
      await _createDefaultPlaylists();

      // Emit initial data
      _songsController.add(songs);
      _playlistsController.add(playlists);
      _albumsController.add(albums);

    } catch (e) {
      print('Error initializing MusicLibraryService: $e');
    }
  }

  Future<void> _createDefaultPlaylists() async {
    final defaultPlaylists = [
      Playlist(
        id: 'favorites',
        name: 'Favorites',
        description: 'Your favorite songs',
        songIds: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSystemPlaylist: true,
        type: PlaylistType.favorites,
      ),
      Playlist(
        id: 'recently_played',
        name: 'Recently Played',
        description: 'Songs you\'ve played recently',
        songIds: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSystemPlaylist: true,
        type: PlaylistType.recentlyPlayed,
      ),
      Playlist(
        id: 'most_played',
        name: 'Most Played',
        description: 'Your most played songs',
        songIds: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSystemPlaylist: true,
        type: PlaylistType.mostPlayed,
      ),
      Playlist(
        id: 'recently_added',
        name: 'Recently Added',
        description: 'Songs you\'ve added recently',
        songIds: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSystemPlaylist: true,
        type: PlaylistType.recentlyAdded,
      ),
    ];

    for (final playlist in defaultPlaylists) {
      if (!_playlistsBox.containsKey(playlist.id)) {
        await _playlistsBox.put(playlist.id, playlist);
      }
    }
  }

  // Song operations
  Future<void> addSong(Song song) async {
    await _songsBox.put(song.id, song);
    await _updateRecentlyAddedPlaylist(song.id);
    await _updateAlbums(song);
    _songsController.add(songs);
    _albumsController.add(albums);
  }

  Future<void> addSongs(List<Song> newSongs) async {
    for (final song in newSongs) {
      await _songsBox.put(song.id, song);
      await _updateAlbums(song);
    }
    
    // Update recently added playlist
    final recentlyAdded = _playlistsBox.get('recently_added');
    if (recentlyAdded != null) {
      final updatedSongIds = [...recentlyAdded.songIds];
      for (final song in newSongs) {
        if (!updatedSongIds.contains(song.id)) {
          updatedSongIds.insert(0, song.id);
        }
      }
      // Keep only the 50 most recent
      if (updatedSongIds.length > 50) {
        updatedSongIds.removeRange(50, updatedSongIds.length);
      }
      
      await _playlistsBox.put('recently_added', recentlyAdded.copyWith(
        songIds: updatedSongIds,
        updatedAt: DateTime.now(),
      ));
    }

    _songsController.add(songs);
    _albumsController.add(albums);
    _playlistsController.add(playlists);
  }

  Future<void> updateSong(Song song) async {
    await _songsBox.put(song.id, song);
    await _updateAlbums(song);
    _songsController.add(songs);
    _albumsController.add(albums);
  }

  Future<void> deleteSong(String songId) async {
    await _songsBox.delete(songId);
    
    // Remove from all playlists
    for (final playlist in playlists) {
      if (playlist.songIds.contains(songId)) {
        final updatedPlaylist = playlist.removeSong(songId);
        await _playlistsBox.put(playlist.id, updatedPlaylist);
      }
    }

    _songsController.add(songs);
    _playlistsController.add(playlists);
  }

  Song? getSong(String songId) {
    return _songsBox.get(songId);
  }

  List<Song> searchSongs(String query) {
    if (query.isEmpty) return songs;
    
    final lowercaseQuery = query.toLowerCase();
    return songs.where((song) {
      return song.title.toLowerCase().contains(lowercaseQuery) ||
             song.artist.toLowerCase().contains(lowercaseQuery) ||
             (song.album?.toLowerCase().contains(lowercaseQuery) ?? false) ||
             (song.genre?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  // Playlist operations
  Future<void> createPlaylist(Playlist playlist) async {
    await _playlistsBox.put(playlist.id, playlist);
    _playlistsController.add(playlists);
  }

  Future<void> updatePlaylist(Playlist playlist) async {
    await _playlistsBox.put(playlist.id, playlist);
    _playlistsController.add(playlists);
  }

  Future<void> deletePlaylist(String playlistId) async {
    final playlist = _playlistsBox.get(playlistId);
    if (playlist != null && !playlist.isSystemPlaylist) {
      await _playlistsBox.delete(playlistId);
      _playlistsController.add(playlists);
    }
  }

  Playlist? getPlaylist(String playlistId) {
    return _playlistsBox.get(playlistId);
  }

  Future<void> addSongToPlaylist(String playlistId, String songId) async {
    final playlist = _playlistsBox.get(playlistId);
    if (playlist != null) {
      final updatedPlaylist = playlist.addSong(songId);
      await _playlistsBox.put(playlistId, updatedPlaylist);
      _playlistsController.add(playlists);
    }
  }

  Future<void> removeSongFromPlaylist(String playlistId, String songId) async {
    final playlist = _playlistsBox.get(playlistId);
    if (playlist != null) {
      final updatedPlaylist = playlist.removeSong(songId);
      await _playlistsBox.put(playlistId, updatedPlaylist);
      _playlistsController.add(playlists);
    }
  }

  // Album operations
  Future<void> _updateAlbums(Song song) async {
    if (song.album == null || song.album!.isEmpty) return;

    final albumId = '${song.artist}_${song.album}'.toLowerCase().replaceAll(' ', '_');
    Album? existingAlbum = _albumsBox.get(albumId);

    if (existingAlbum == null) {
      final newAlbum = Album(
        id: albumId,
        name: song.album!,
        artist: song.artist,
        albumArt: song.albumArt,
        year: song.year,
        genre: song.genre,
        songIds: [song.id],
        dateAdded: DateTime.now(),
      );
      await _albumsBox.put(albumId, newAlbum);
    } else {
      if (!existingAlbum.songIds.contains(song.id)) {
        final updatedAlbum = existingAlbum.copyWith(
          songIds: [...existingAlbum.songIds, song.id],
        );
        await _albumsBox.put(albumId, updatedAlbum);
      }
    }
  }

  List<Album> searchAlbums(String query) {
    if (query.isEmpty) return albums;
    
    final lowercaseQuery = query.toLowerCase();
    return albums.where((album) {
      return album.name.toLowerCase().contains(lowercaseQuery) ||
             album.artist.toLowerCase().contains(lowercaseQuery) ||
             (album.genre?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  // Utility methods
  Future<void> _updateRecentlyAddedPlaylist(String songId) async {
    final recentlyAdded = _playlistsBox.get('recently_added');
    if (recentlyAdded != null) {
      final updatedSongIds = [songId, ...recentlyAdded.songIds];
      if (updatedSongIds.length > 50) {
        updatedSongIds.removeRange(50, updatedSongIds.length);
      }
      
      await _playlistsBox.put('recently_added', recentlyAdded.copyWith(
        songIds: updatedSongIds,
        updatedAt: DateTime.now(),
      ));
    }
  }

  Future<void> updateRecentlyPlayedPlaylist(String songId) async {
    final recentlyPlayed = _playlistsBox.get('recently_played');
    if (recentlyPlayed != null) {
      final updatedSongIds = [songId, ...recentlyPlayed.songIds.where((id) => id != songId)];
      if (updatedSongIds.length > 50) {
        updatedSongIds.removeRange(50, updatedSongIds.length);
      }
      
      await _playlistsBox.put('recently_played', recentlyPlayed.copyWith(
        songIds: updatedSongIds,
        updatedAt: DateTime.now(),
      ));
      _playlistsController.add(playlists);
    }
  }

  Future<void> updateMostPlayedPlaylist() async {
    final mostPlayedSongs = songs
        .where((song) => song.playCount > 0)
        .toList()
      ..sort((a, b) => b.playCount.compareTo(a.playCount));

    final mostPlayed = _playlistsBox.get('most_played');
    if (mostPlayed != null) {
      final updatedSongIds = mostPlayedSongs.take(50).map((song) => song.id).toList();
      
      await _playlistsBox.put('most_played', mostPlayed.copyWith(
        songIds: updatedSongIds,
        updatedAt: DateTime.now(),
      ));
      _playlistsController.add(playlists);
    }
  }

  void dispose() {
    _songsController.close();
    _playlistsController.close();
    _albumsController.close();
  }
}
