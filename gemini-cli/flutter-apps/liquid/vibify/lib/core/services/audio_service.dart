import 'dart:async';
import 'package:just_audio/just_audio.dart';
import 'package:audio_session/audio_session.dart';
import '../../data/models/song.dart';

enum PlaybackState {
  stopped,
  playing,
  paused,
  buffering,
  loading,
  error,
}

enum RepeatMode {
  off,
  one,
  all,
}

enum ShuffleMode {
  off,
  on,
}

class AudioPlayerService {
  static final AudioPlayerService _instance = AudioPlayerService._internal();
  factory AudioPlayerService() => _instance;
  AudioPlayerService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final StreamController<PlaybackState> _playbackStateController = 
      StreamController<PlaybackState>.broadcast();
  final StreamController<Song?> _currentSongController = 
      StreamController<Song?>.broadcast();
  final StreamController<Duration> _positionController = 
      StreamController<Duration>.broadcast();
  final StreamController<Duration> _durationController = 
      StreamController<Duration>.broadcast();
  final StreamController<double> _volumeController = 
      StreamController<double>.broadcast();
  final StreamController<RepeatMode> _repeatModeController = 
      StreamController<RepeatMode>.broadcast();
  final StreamController<ShuffleMode> _shuffleModeController = 
      StreamController<ShuffleMode>.broadcast();

  List<Song> _playlist = [];
  int _currentIndex = 0;
  Song? _currentSong;
  PlaybackState _playbackState = PlaybackState.stopped;
  RepeatMode _repeatMode = RepeatMode.off;
  ShuffleMode _shuffleMode = ShuffleMode.off;
  double _volume = 1.0;

  // Getters
  Stream<PlaybackState> get playbackStateStream => _playbackStateController.stream;
  Stream<Song?> get currentSongStream => _currentSongController.stream;
  Stream<Duration> get positionStream => _positionController.stream;
  Stream<Duration> get durationStream => _durationController.stream;
  Stream<double> get volumeStream => _volumeController.stream;
  Stream<RepeatMode> get repeatModeStream => _repeatModeController.stream;
  Stream<ShuffleMode> get shuffleModeStream => _shuffleModeController.stream;

  Song? get currentSong => _currentSong;
  PlaybackState get playbackState => _playbackState;
  RepeatMode get repeatMode => _repeatMode;
  ShuffleMode get shuffleMode => _shuffleMode;
  double get volume => _volume;
  List<Song> get playlist => List.unmodifiable(_playlist);
  int get currentIndex => _currentIndex;
  Duration get position => _audioPlayer.position;
  Duration get duration => _audioPlayer.duration ?? Duration.zero;

  Future<void> initialize() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());

      // Listen to player state changes
      _audioPlayer.playerStateStream.listen((playerState) {
        _updatePlaybackState(playerState);
      });

      // Listen to position changes
      _audioPlayer.positionStream.listen((position) {
        _positionController.add(position);
      });

      // Listen to duration changes
      _audioPlayer.durationStream.listen((duration) {
        if (duration != null) {
          _durationController.add(duration);
        }
      });

      // Listen to sequence state changes for automatic next track
      _audioPlayer.sequenceStateStream.listen((sequenceState) {
        if (sequenceState != null) {
          _currentIndex = sequenceState.currentIndex ?? 0;
          if (_currentIndex < _playlist.length) {
            _currentSong = _playlist[_currentIndex];
            _currentSongController.add(_currentSong);
          }
        }
      });

      // Set initial volume
      await _audioPlayer.setVolume(_volume);
      _volumeController.add(_volume);

    } catch (e) {
      _updatePlaybackState(PlayerState(false, ProcessingState.idle));
    }
  }

  void _updatePlaybackState(PlayerState playerState) {
    PlaybackState newState;
    
    if (playerState.processingState == ProcessingState.loading ||
        playerState.processingState == ProcessingState.buffering) {
      newState = PlaybackState.buffering;
    } else if (playerState.processingState == ProcessingState.ready) {
      newState = playerState.playing ? PlaybackState.playing : PlaybackState.paused;
    } else if (playerState.processingState == ProcessingState.completed) {
      newState = PlaybackState.stopped;
      _handleTrackCompletion();
    } else {
      newState = PlaybackState.stopped;
    }

    if (_playbackState != newState) {
      _playbackState = newState;
      _playbackStateController.add(_playbackState);
    }
  }

  Future<void> _handleTrackCompletion() async {
    switch (_repeatMode) {
      case RepeatMode.one:
        await seek(Duration.zero);
        await play();
        break;
      case RepeatMode.all:
        await skipToNext();
        break;
      case RepeatMode.off:
        if (_currentIndex < _playlist.length - 1) {
          await skipToNext();
        } else {
          await stop();
        }
        break;
    }
  }

  Future<void> setPlaylist(List<Song> songs, {int initialIndex = 0}) async {
    try {
      _playlist = List.from(songs);
      _currentIndex = initialIndex.clamp(0, _playlist.length - 1);
      
      if (_playlist.isNotEmpty) {
        final audioSources = _playlist.map((song) => 
            AudioSource.file(song.filePath)).toList();
        
        await _audioPlayer.setAudioSource(
          ConcatenatingAudioSource(children: audioSources),
          initialIndex: _currentIndex,
        );
        
        _currentSong = _playlist[_currentIndex];
        _currentSongController.add(_currentSong);
      }
    } catch (e) {
      _playbackState = PlaybackState.error;
      _playbackStateController.add(_playbackState);
    }
  }

  Future<void> play() async {
    try {
      await _audioPlayer.play();
    } catch (e) {
      _playbackState = PlaybackState.error;
      _playbackStateController.add(_playbackState);
    }
  }

  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      _playbackState = PlaybackState.error;
      _playbackStateController.add(_playbackState);
    }
  }

  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
    } catch (e) {
      _playbackState = PlaybackState.error;
      _playbackStateController.add(_playbackState);
    }
  }

  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      // Handle seek error
    }
  }

  Future<void> skipToNext() async {
    if (_playlist.isEmpty) return;
    
    int nextIndex;
    if (_shuffleMode == ShuffleMode.on) {
      nextIndex = _getRandomIndex();
    } else {
      nextIndex = (_currentIndex + 1) % _playlist.length;
    }
    
    await skipToIndex(nextIndex);
  }

  Future<void> skipToPrevious() async {
    if (_playlist.isEmpty) return;
    
    int previousIndex;
    if (_shuffleMode == ShuffleMode.on) {
      previousIndex = _getRandomIndex();
    } else {
      previousIndex = (_currentIndex - 1 + _playlist.length) % _playlist.length;
    }
    
    await skipToIndex(previousIndex);
  }

  Future<void> skipToIndex(int index) async {
    if (_playlist.isEmpty || index < 0 || index >= _playlist.length) return;
    
    try {
      await _audioPlayer.seek(Duration.zero, index: index);
      _currentIndex = index;
      _currentSong = _playlist[_currentIndex];
      _currentSongController.add(_currentSong);
    } catch (e) {
      _playbackState = PlaybackState.error;
      _playbackStateController.add(_playbackState);
    }
  }

  int _getRandomIndex() {
    if (_playlist.length <= 1) return 0;
    int randomIndex;
    do {
      randomIndex = DateTime.now().millisecondsSinceEpoch % _playlist.length;
    } while (randomIndex == _currentIndex);
    return randomIndex;
  }

  Future<void> setVolume(double volume) async {
    _volume = volume.clamp(0.0, 1.0);
    await _audioPlayer.setVolume(_volume);
    _volumeController.add(_volume);
  }

  void setRepeatMode(RepeatMode mode) {
    _repeatMode = mode;
    _repeatModeController.add(_repeatMode);
  }

  void setShuffleMode(ShuffleMode mode) {
    _shuffleMode = mode;
    _shuffleModeController.add(_shuffleMode);
  }

  void dispose() {
    _audioPlayer.dispose();
    _playbackStateController.close();
    _currentSongController.close();
    _positionController.close();
    _durationController.close();
    _volumeController.close();
    _repeatModeController.close();
    _shuffleModeController.close();
  }
}
