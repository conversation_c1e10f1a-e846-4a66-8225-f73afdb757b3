import 'package:flutter/material.dart';

class AppColors {
  // Liquid-inspired primary colors
  static const Color liquidBlue = Color(0xFF1E3A8A);
  static const Color liquidCyan = Color(0xFF06B6D4);
  static const Color liquidTeal = Color(0xFF0D9488);
  static const Color liquidPurple = Color(0xFF7C3AED);
  static const Color liquidPink = Color(0xFFEC4899);
  static const Color liquidIndigo = Color(0xFF4F46E5);

  // Gradient colors for liquid effects
  static const List<Color> primaryGradient = [
    Color(0xFF667EEA),
    Color(0xFF764BA2),
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF11998E),
    Color(0xFF38EF7D),
  ];

  static const List<Color> accentGradient = [
    Color(0xFFFF6B6B),
    Color(0xFF4ECDC4),
  ];

  static const List<Color> darkGradient = [
    Color(0xFF2C3E50),
    Color(0xFF4A6741),
  ];

  // Light theme colors
  static const Color lightBackground = Color(0xFFF8FAFC);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightCardBackground = Color(0xFFF1F5F9);
  static const Color lightTextPrimary = Color(0xFF1E293B);
  static const Color lightTextSecondary = Color(0xFF64748B);
  static const Color lightBorder = Color(0xFFE2E8F0);

  // Dark theme colors
  static const Color darkBackground = Color(0xFF0F172A);
  static const Color darkSurface = Color(0xFF1E293B);
  static const Color darkCardBackground = Color(0xFF334155);
  static const Color darkTextPrimary = Color(0xFFF1F5F9);
  static const Color darkTextSecondary = Color(0xFF94A3B8);
  static const Color darkBorder = Color(0xFF475569);

  // Accent colors
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);

  // Glass morphism colors
  static const Color glassLight = Color(0x1AFFFFFF);
  static const Color glassDark = Color(0x1A000000);
  static const Color glassBlur = Color(0x80FFFFFF);

  // Liquid wave colors
  static const List<Color> waveColors = [
    Color(0xFF667EEA),
    Color(0xFF764BA2),
    Color(0xFF11998E),
    Color(0xFF38EF7D),
    Color(0xFFFF6B6B),
    Color(0xFF4ECDC4),
  ];

  // Music player specific colors
  static const Color playButtonGradientStart = Color(0xFF667EEA);
  static const Color playButtonGradientEnd = Color(0xFF764BA2);
  static const Color progressBarActive = Color(0xFF667EEA);
  static const Color progressBarInactive = Color(0x33667EEA);
  static const Color volumeSliderActive = Color(0xFF11998E);
  static const Color volumeSliderInactive = Color(0x3311998E);

  // Shimmer colors
  static const Color shimmerBase = Color(0xFFE2E8F0);
  static const Color shimmerHighlight = Color(0xFFF1F5F9);
  static const Color shimmerBaseDark = Color(0xFF334155);
  static const Color shimmerHighlightDark = Color(0xFF475569);
}
